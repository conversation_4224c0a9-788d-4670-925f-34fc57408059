/* Shared Page Layout Styles */

/* Container */
.page-container {
  padding: 1rem 2rem;
  width: 100%;
  margin: 0 auto;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  overflow-x: auto;
}

/* Header Section */
.page-header {
  margin-bottom: 2rem;
}

.header-content {
  text-align: center;
  padding: 2rem 0;
}

.header-info {
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.page-title i {
  color: #e036af;
  -webkit-text-fill-color: #e036af;
}

.page-description {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* Filters Section */
.filters-section {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto 2rem;
}

.filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label,
.filter-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto 1rem;
}

.stat-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.stat-icon.active {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Table Section */
.table-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.table-card {
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

/* Search Input */
.search-input {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.1) 100%);
  border: 1px solid rgba(85, 33, 190, 0.15);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  padding: 0.6rem 1rem 0.6rem 2.25rem;
  font-size: 0.875rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 500;
  color: #374151;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.08);
  min-width: 220px;
  width: 100%;
}

.search-input:hover {
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(254, 236, 249, 0.2) 100%);
  box-shadow: 0 4px 16px rgba(85, 33, 190, 0.12);
  transform: translateY(-1px);
}

.search-input:focus {
  outline: none;
  border-color: #5521be;
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 236, 249, 0.15) 100%);
  box-shadow: 0 0 0 3px rgba(85, 33, 190, 0.15), 0 4px 16px rgba(85, 33, 190, 0.12);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #9ca3af;
  font-style: italic;
  font-weight: 400;
}

/* Search Icon Styling */
.p-input-icon-left > i {
  color: #9ca3af;
  left: 0.75rem;
  transition: color 0.3s ease;
  font-size: 0.875rem;
}

.p-input-icon-left:hover > i,
.p-input-icon-left:focus-within > i {
  color: #5521be;
}

/* Filter Dropdown */
.filter-dropdown {
  width: 100%;
}

/* Refresh Button */
.refresh-button {
  width: 100%;
}

/* Table Styles */
::ng-deep .p-datatable {
  border-radius: 12px;
  overflow: hidden;
  border: none;
  min-width: 1200px;
}

::ng-deep .p-datatable .p-datatable-wrapper {
  overflow-x: auto;
}

::ng-deep .p-datatable .p-datatable-thead > tr > th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: none;
  border-bottom: 2px solid #e2e8f0;
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 0.75rem;
}

::ng-deep .p-datatable .p-datatable-tbody > tr {
  transition: background-color 0.2s ease;
}

::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
  background-color: rgba(85, 33, 190, 0.05) !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  border: none;
  border-bottom: 1px solid #f1f5f9;
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

/* File Type Icons */
.file-type-video {
  color: #ef4444;
  font-size: 1.25rem;
}

.file-type-image {
  color: #10b981;
  font-size: 1.25rem;
}

.file-type-other {
  color: #6b7280;
  font-size: 1.25rem;
}

/* File Name Cell */
.file-name-cell .file-name {
  font-weight: 500;
  color: #1f2937;
  display: block;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-name-cell .error-message {
  color: #ef4444;
  font-size: 0.8rem;
  display: block;
  margin-top: 0.25rem;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* File Size */
.file-size {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #6b7280;
}

/* Status Tag */
.status-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-spinner {
  font-size: 0.8rem;
}

/* Progress Cell */
.progress-cell .progress-text {
  color: #6b7280;
  font-size: 0.8rem;
}

/* Date Text */
.date-text {
  font-size: 0.9rem;
  color: #6b7280;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  width: 32px;
  height: 32px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-icon {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.empty-state p {
  color: #6b7280;
  margin: 0;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 2rem;
}

.loading-state p {
  margin-top: 1rem;
  color: #6b7280;
}

/* Global dropdown z-index fix */
.p-dropdown-panel,
.p-multiselect-panel,
.p-autocomplete-panel,
.p-calendar-panel {
  z-index: 9999 !important;
}

/* Global status tag overrides to prevent Font Awesome conflicts */
.p-tag.p-tag-secondary {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%) !important;
  color: white !important;
}

.p-tag.p-tag-info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  color: white !important;
}

.p-tag.p-tag-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
}

.p-tag.p-tag-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
}

.p-tag.p-tag-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: white !important;
}

.p-tag {
  font-weight: 600 !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  border-radius: 12px !important;
  padding: 0.5rem 0.75rem !important;
  border: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-container {
    padding: 1rem;
  }

  .header-content {
    padding: 1rem 0;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

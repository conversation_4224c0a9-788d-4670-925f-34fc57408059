export interface CreativesUploaderConfig {
  maxFileSize: number; // MB
  allowedFormats: string[];
  autoOptimize: boolean;
  compressionQuality: number; // 1-100
  enableBulkUpload: boolean;
  maxFilesPerUpload: number;
  enablePreview: boolean;
  autoTagging: boolean;
  defaultTags: string[];
}

export const defaultCreativesUploaderConfig: CreativesUploaderConfig = {
  maxFileSize: 50, // 50MB
  allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'mov', 'avi'],
  autoOptimize: true,
  compressionQuality: 85,
  enableBulkUpload: true,
  maxFilesPerUpload: 20,
  enablePreview: true,
  autoTagging: false,
  defaultTags: [],
};

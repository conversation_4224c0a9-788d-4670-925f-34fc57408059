import { Injectable } from '@angular/core';
import { catchError, from, map, Observable, throwError } from 'rxjs';
import { SupabaseService } from '../../../core/services';
import {
  CreativeFile,
  CreativeFileFilters,
  CreativeFileResponse,
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class CreativeFileService {
  constructor(private supabaseService: SupabaseService) {}

  getCreativeFiles(
    filters: CreativeFileFilters = {},
  ): Observable<CreativeFileResponse> {
    let query = this.supabaseService.client
      .from('fb_creative_files')
      .select('*', { count: 'exact' })
      .order('uploaded_at', { ascending: false, nullsFirst: true })
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.mime_type) {
      query = query.like('mime_type', `${filters.mime_type}%`);
    }

    if (filters.search) {
      query = query.ilike('file_name', `%${filters.search}%`);
    }

    return from(query).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching creative files:', response.error);
          throw response.error;
        }

        return {
          data: response.data || [],
          total_count: response.count || 0,
        };
      }),
      catchError((error) => {
        console.error('Error fetching creative files:', error);
        return throwError(() => error);
      }),
    );
  }

  hasActiveUploads(files: CreativeFile[]): boolean {
    return files.some(
      (file) =>
        file.status === 'downloading' ||
        file.status === 'uploading' ||
        file.status === 'pending',
    );
  }

  getActiveUploadsCount(files: CreativeFile[]): number {
    return files.filter(
      (file) =>
        file.status === 'downloading' ||
        file.status === 'uploading' ||
        file.status === 'pending',
    ).length;
  }
}

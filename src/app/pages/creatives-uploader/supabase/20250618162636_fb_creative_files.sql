CREATE TABLE fb_creative_files
(
  id                   UUID                     DEFAULT gen_random_uuid() PRIMARY KEY,

  google_drive_file_id VARCHAR(255)                               NOT NULL UNIQUE,
  file_name            VARCHAR(255)                               NOT NULL,
  file_size            BIGINT,
  mime_type            VARCHAR(100)                               NOT NULL,

  status               VARCHAR(20)              DEFAULT 'pending' NOT NULL,
  fb_creative_id       VARCHAR(100),
  upload_error         TEXT,

  created_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW()     NOT NULL,
  updated_at           TIMESTAMP WITH TIME ZONE DEFAULT NOW()     NOT NULL,
  uploaded_at          TIMESTAMP WITH TIME ZONE,

  CONSTRAINT valid_status CHECK (status IN ('pending', 'downloading', 'uploading', 'uploaded', 'failed'))
);

-- Indexes for better performance
CREATE INDEX idx_fb_creative_files_status ON fb_creative_files(status);
CREATE INDEX idx_fb_creative_files_google_drive_id ON fb_creative_files(google_drive_file_id);
CREATE INDEX idx_fb_creative_files_created_at ON fb_creative_files(created_at);
CREATE INDEX idx_fb_creative_files_user_id ON fb_creative_files(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE fb_creative_files ENABLE ROW LEVEL SECURITY;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_fb_creative_files_updated_at
  BEFORE UPDATE ON fb_creative_files
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

export interface AdWinnersConfig {
  defaultMetrics: string[];
  maxResults: number;
  autoRefresh: boolean;
  refreshInterval: number; // milliseconds
  showCalculationFormulas: boolean;
  enableComparison: boolean;
  defaultTimeRange: string;
  maxComparisons: number;
}

export const defaultAdWinnersConfig: AdWinnersConfig = {
  defaultMetrics: ['ctr', 'cpm', 'roas'],
  maxResults: 50,
  autoRefresh: true,
  refreshInterval: 30000, // 30 seconds
  showCalculationFormulas: true,
  enableComparison: true,
  defaultTimeRange: 'last_7_days',
  maxComparisons: 3,
};

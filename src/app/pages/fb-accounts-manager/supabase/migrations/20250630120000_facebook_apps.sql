-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create facebook_apps table
CREATE TABLE facebook_apps (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    app_id TEXT NOT NULL UNIQUE,
    app_secret TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create facebook_users table (renamed from facebook_tokens)
CREATE TABLE facebook_users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    app_id TEXT NOT NULL REFERENCES facebook_apps(app_id) ON DELETE CASCADE,
    access_token TEXT NOT NULL,
    facebook_user_id TEXT NOT NULL, -- Facebook user ID
    name TEXT NOT NULL, -- User's full name
    email TEXT, -- User's email (if available)
    profile_picture_url TEXT, -- Profile picture URL
    is_long_lived BOOLEAN DEFAULT TRUE,
    permissions TEXT[], -- Array of granted permissions
    expires_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure one user per app
    UNIQUE(app_id, facebook_user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_facebook_apps_app_id ON facebook_apps(app_id);
CREATE INDEX idx_facebook_apps_is_active ON facebook_apps(is_active);

CREATE INDEX idx_facebook_users_app_id ON facebook_users(app_id);
CREATE INDEX idx_facebook_users_facebook_user_id ON facebook_users(facebook_user_id);
CREATE INDEX idx_facebook_users_expires_at ON facebook_users(expires_at);
CREATE INDEX idx_facebook_users_last_login ON facebook_users(last_login);

-- Enable Row Level Security (RLS)
ALTER TABLE facebook_apps ENABLE ROW LEVEL SECURITY;
ALTER TABLE facebook_users ENABLE ROW LEVEL SECURITY;

-- Create policies for facebook_apps - allow all operations for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON facebook_apps
    FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for facebook_users - allow all operations for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON facebook_users
    FOR ALL USING (auth.role() = 'authenticated');

-- Create function to automatically set updated_at
CREATE OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Always update updated_at
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for facebook_apps
CREATE TRIGGER facebook_apps_set_updated_at
    BEFORE INSERT OR UPDATE ON facebook_apps
    FOR EACH ROW
    EXECUTE FUNCTION set_updated_at();

-- Create triggers for facebook_users
CREATE TRIGGER facebook_users_set_updated_at
    BEFORE INSERT OR UPDATE ON facebook_users
    FOR EACH ROW
    EXECUTE FUNCTION set_updated_at();

-- Add comments for documentation
COMMENT ON TABLE facebook_apps IS 'Facebook applications configured by users';
COMMENT ON TABLE facebook_users IS 'Facebook user accounts connected to apps';

COMMENT ON COLUMN facebook_apps.app_id IS 'Facebook App ID from Facebook Developer Console';
COMMENT ON COLUMN facebook_apps.app_secret IS 'Facebook App Secret from Facebook Developer Console';

COMMENT ON COLUMN facebook_users.facebook_user_id IS 'Facebook User ID from Facebook Graph API';
COMMENT ON COLUMN facebook_users.name IS 'User full name from Facebook profile';
COMMENT ON COLUMN facebook_users.email IS 'User email address (if permission granted)';
COMMENT ON COLUMN facebook_users.profile_picture_url IS 'URL to user profile picture';
COMMENT ON COLUMN facebook_users.is_long_lived IS 'Whether this is a long-lived token (60 days)';
COMMENT ON COLUMN facebook_users.permissions IS 'Array of Facebook permissions granted to this user';
COMMENT ON COLUMN facebook_users.last_login IS 'Timestamp of last successful login';

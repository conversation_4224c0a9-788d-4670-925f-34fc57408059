-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create facebook_apps table
CREATE TABLE facebook_apps (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    app_id TEXT NOT NULL UNIQUE,
    app_secret TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create facebook_tokens table
CREATE TABLE facebook_tokens (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    app_id TEXT NOT NULL REFERENCES facebook_apps(app_id) ON DELETE CASCADE,
    access_token TEXT NOT NULL,
    token_type TEXT NOT NULL CHECK (token_type IN ('user', 'page', 'app')),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_long_lived BOOLEAN DEFAULT FALSE,
    permissions TEXT[], -- Array of permissions
    page_id TEXT, -- Facebook page ID if this is a page token
    page_name TEXT, -- Facebook page name if this is a page token
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_facebook_apps_app_id ON facebook_apps(app_id);
CREATE INDEX idx_facebook_apps_is_active ON facebook_apps(is_active);

CREATE INDEX idx_facebook_tokens_app_id ON facebook_tokens(app_id);
CREATE INDEX idx_facebook_tokens_token_type ON facebook_tokens(token_type);
CREATE INDEX idx_facebook_tokens_page_id ON facebook_tokens(page_id);
CREATE INDEX idx_facebook_tokens_expires_at ON facebook_tokens(expires_at);

-- Enable Row Level Security (RLS)
ALTER TABLE facebook_apps ENABLE ROW LEVEL SECURITY;
ALTER TABLE facebook_tokens ENABLE ROW LEVEL SECURITY;

-- Create policies for facebook_apps - allow all operations for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON facebook_apps
    FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for facebook_tokens - allow all operations for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON facebook_tokens
    FOR ALL USING (auth.role() = 'authenticated');

-- Create function to automatically set updated_at
CREATE OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Always update updated_at
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for facebook_apps
CREATE TRIGGER facebook_apps_set_updated_at
    BEFORE INSERT OR UPDATE ON facebook_apps
    FOR EACH ROW
    EXECUTE FUNCTION set_updated_at();

-- Create triggers for facebook_tokens
CREATE TRIGGER facebook_tokens_set_updated_at
    BEFORE INSERT OR UPDATE ON facebook_tokens
    FOR EACH ROW
    EXECUTE FUNCTION set_updated_at();

-- Add comments for documentation
COMMENT ON TABLE facebook_apps IS 'Facebook applications configured by users';
COMMENT ON TABLE facebook_tokens IS 'Facebook access tokens for apps, users, and pages';

COMMENT ON COLUMN facebook_apps.app_id IS 'Facebook App ID from Facebook Developer Console';
COMMENT ON COLUMN facebook_apps.app_secret IS 'Facebook App Secret from Facebook Developer Console';

COMMENT ON COLUMN facebook_tokens.token_type IS 'Type of token: user, page, or app';
COMMENT ON COLUMN facebook_tokens.is_long_lived IS 'Whether this is a long-lived token (60 days)';
COMMENT ON COLUMN facebook_tokens.permissions IS 'Array of Facebook permissions granted to this token';
COMMENT ON COLUMN facebook_tokens.page_id IS 'Facebook Page ID if this is a page token';
COMMENT ON COLUMN facebook_tokens.page_name IS 'Facebook Page name if this is a page token';

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface FacebookApp {
  id: string
  app_id: string
  name: string
  description?: string
  is_active: boolean
  created_at: string
}

interface FacebookUser {
  id: string
  app_id: string
  facebook_user_id: string
  name: string
  email?: string
  profile_picture_url?: string
  is_long_lived: boolean
  permissions?: string[]
  expires_at?: string
  last_login?: string
  created_at: string
}

interface FacebookAdAccount {
  id: string
  name: string
  account_status: number
  currency: string
  timezone_name?: string
  business_id?: string
  business_name?: string
}

interface FacebookAccountsStructure {
  apps: Array<{
    app: FacebookApp
    users: Array<{
      user: FacebookUser
      adAccounts: FacebookAdAccount[]
    }>
  }>
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    // Get all Facebook apps
    const { data: apps, error: appsError } = await supabaseClient
      .from('facebook_apps')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (appsError) {
      throw new Error(`Failed to fetch apps: ${appsError.message}`)
    }

    // Get all Facebook users (without access tokens for response)
    const { data: users, error: usersError } = await supabaseClient
      .from('facebook_users')
      .select(`
        id,
        app_id,
        facebook_user_id,
        name,
        email,
        profile_picture_url,
        is_long_lived,
        permissions,
        expires_at,
        last_login,
        created_at
      `)
      .order('last_login', { ascending: false })

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`)
    }

    // Get users with tokens for API calls (service role can access all data)
    const { data: usersWithTokens, error: tokensError } = await supabaseClient
      .from('facebook_users')
      .select('id, access_token, facebook_user_id')

    if (tokensError) {
      throw new Error(`Failed to fetch user tokens: ${tokensError.message}`)
    }

    // Build the structure
    const structure: FacebookAccountsStructure = {
      apps: []
    }

    for (const app of apps || []) {
      const appUsers = users?.filter(user => user.app_id === app.app_id) || []
      
      const usersWithAccounts = await Promise.all(
        appUsers.map(async (user) => {
          // Find the token for this user
          const userToken = usersWithTokens?.find(t => t.id === user.id)
          
          let adAccounts: FacebookAdAccount[] = []
          
          if (userToken?.access_token) {
            try {
              // Fetch ad accounts from Facebook Graph API
              adAccounts = await fetchAdAccountsForUser(userToken.access_token)
            } catch (error) {
              console.error(`Failed to fetch ad accounts for user ${user.facebook_user_id}:`, error)
              // Continue with empty accounts array
            }
          }

          return {
            user,
            adAccounts
          }
        })
      )

      structure.apps.push({
        app,
        users: usersWithAccounts
      })
    }

    return new Response(
      JSON.stringify(structure),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error in facebook-accounts-structure function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

async function fetchAdAccountsForUser(accessToken: string): Promise<FacebookAdAccount[]> {
  try {
    const response = await fetch(
      `https://graph.facebook.com/v18.0/me/adaccounts?access_token=${accessToken}&fields=id,name,account_status,currency,timezone_name,business{id,name}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )

    if (!response.ok) {
      throw new Error(`Graph API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(`Graph API error: ${data.error.message}`)
    }

    return (data.data || []).map((account: any) => ({
      id: account.id,
      name: account.name,
      account_status: account.account_status,
      currency: account.currency,
      timezone_name: account.timezone_name,
      business_id: account.business?.id,
      business_name: account.business?.name,
    }))

  } catch (error) {
    console.error('Error fetching ad accounts:', error)
    return []
  }
}

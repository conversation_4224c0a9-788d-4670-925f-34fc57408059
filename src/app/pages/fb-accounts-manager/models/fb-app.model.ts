export interface FacebookApp {
  id?: string;
  name: string;
  app_id: string;
  app_secret: string;
  client_id?: string; // Same as app_id, for clarity
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  user_id?: string;
}

export interface FacebookToken {
  id?: string;
  app_id: string;
  access_token: string;
  token_type: 'user' | 'page' | 'app';
  expires_at?: string;
  is_long_lived: boolean;
  permissions?: string[];
  user_id?: string;
  page_id?: string;
  page_name?: string;
  created_at?: string;
  updated_at?: string;
}

export interface FacebookLoginResponse {
  accessToken: string;
  userID: string;
  expiresIn: number;
  signedRequest: string;
  graphDomain: string;
  data_access_expiration_time: number;
}

export interface FacebookUserInfo {
  id: string;
  name: string;
  email?: string;
  picture?: {
    data: {
      url: string;
    };
  };
}

export interface FacebookPage {
  id: string;
  name: string;
  access_token: string;
  category: string;
  tasks?: string[];
}

export interface FacebookAppConfig {
  app: FacebookApp;
  tokens: FacebookToken[];
  user_info?: FacebookUserInfo;
  pages?: FacebookPage[];
}

export interface FacebookAppFilters {
  is_active?: boolean;
  search?: string;
}

export interface FacebookAppResponse {
  data: FacebookApp[];
  total_count: number;
}

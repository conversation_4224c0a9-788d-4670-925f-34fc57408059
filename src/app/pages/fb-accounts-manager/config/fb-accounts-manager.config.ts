export interface FbAccountsManagerConfig {
  maxApps: number;
  autoTokenRefresh: boolean;
  showExpiredWarnings: boolean;
  tokenExpiryWarningDays: number;
  maxUsersPerApp: number;
  enableAppInstructions: boolean;
  defaultTokenScope: string[];
  autoCleanupExpiredTokens: boolean;
}

export const defaultFbAccountsManagerConfig: FbAccountsManagerConfig = {
  maxApps: 10,
  autoTokenRefresh: true,
  showExpiredWarnings: true,
  tokenExpiryWarningDays: 7,
  maxUsersPerApp: 5,
  enableAppInstructions: true,
  defaultTokenScope: [
    'ads_read',
    'business_management',
    'pages_show_list',
    'ads_management',
    'pages_read_engagement',
    'pages_manage_ads',
    'pages_read_user_content',
    'pages_manage_metadata',
    'pages_manage_posts',
  ],
  autoCleanupExpiredTokens: false,
};

.fb-accounts-manager {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-title i {
  color: #1877f2;
}

.page-subtitle {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.header-actions {
  flex-shrink: 0;
}

/* Content Container */
.content-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Apps Card */
.apps-card {
  background: var(--surface-card);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* Table Styles */
.app-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.app-icon {
  color: #1877f2;
  font-size: 1.1rem;
}

.app-id {
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Empty State */
.empty-message {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.empty-icon {
  font-size: 3rem;
  color: var(--text-color-secondary);
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0;
  color: var(--text-color);
}

.empty-state p {
  margin: 0;
  color: var(--text-color-secondary);
}

/* Dialog Styles */
.app-dialog,
.token-dialog {
  width: 90vw;
  max-width: 600px;
}

.dialog-content {
  padding: 1rem 0;
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.checkbox-field {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-field label {
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
}

/* Token Dialog Specific Styles */
.login-section,
.tokens-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--surface-border);
}

.tokens-section {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-header h3 {
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
}

.section-header p {
  margin: 0;
  color: var(--text-color-secondary);
}

/* Facebook Login Button */
.facebook-login-btn {
  background: #1877f2 !important;
  border-color: #1877f2 !important;
}

.facebook-login-btn:hover {
  background: #166fe5 !important;
  border-color: #166fe5 !important;
}

/* User Info */
.user-info {
  margin-top: 1.5rem;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
  border: 1px solid var(--surface-border);
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
}

.user-details p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.user-id {
  font-family: 'Courier New', monospace;
}

/* Pages Section */
.pages-section {
  margin-top: 1.5rem;
}

.pages-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
}

.pages-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.page-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--surface-50);
  border-radius: 6px;
  border: 1px solid var(--surface-border);
}

.page-icon {
  color: #1877f2;
}

.page-name {
  flex: 1;
  font-weight: 500;
}

/* Tokens List */
.empty-tokens {
  text-align: center;
  padding: 2rem;
  color: var(--text-color-secondary);
}

.tokens-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.token-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
  border: 1px solid var(--surface-border);
}

.token-info {
  flex: 1;
}

.token-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.token-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.token-preview {
  background: var(--surface-100);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  display: inline-block;
}

.token-date {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
}

.token-actions {
  flex-shrink: 0;
  margin-left: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fb-accounts-manager {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .app-dialog,
  .token-dialog {
    width: 95vw;
  }

  .user-card {
    flex-direction: column;
    text-align: center;
  }

  .token-item {
    flex-direction: column;
    gap: 1rem;
  }

  .token-actions {
    margin-left: 0;
    align-self: flex-end;
  }

  .action-buttons {
    flex-wrap: wrap;
  }
}

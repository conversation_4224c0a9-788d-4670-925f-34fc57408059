.fb-accounts-manager {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-title i {
  color: #1877f2;
}

.page-subtitle {
  color: var(--text-color-secondary);
  margin: 0;
  font-size: 1.1rem;
}

.header-actions {
  flex-shrink: 0;
}

/* Content Container */
.content-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Apps Section */
.apps-section {
  width: 100%;
}

.section-title {
  margin-bottom: 2rem;
}

.section-title h2 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.section-title p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1rem;
}

/* Loading Grid */
.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

.app-card-skeleton {
  width: 100%;
}

/* Apps Grid */
.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

/* App Card */
.app-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid var(--surface-border);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.app-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.app-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-text));
}

/* App Header */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.app-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1877f2, #166fe5);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
}

.app-details {
  flex: 1;
}

.app-name {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.2rem;
  font-weight: 600;
}

.app-id {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: var(--text-color);
  border: 1px solid var(--surface-border);
  display: inline-block;
}

.app-status {
  flex-shrink: 0;
}

/* App Meta */
.app-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
  border: 1px solid var(--surface-border);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.meta-item i {
  color: var(--primary-color);
  font-size: 0.85rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #feecf9 0%, #f8f4ff 100%);
  border-radius: 16px;
  border: 2px dashed rgba(85, 33, 190, 0.2);
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1877f2, #166fe5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
  color: white;
  font-size: 2rem;
  box-shadow: 0 8px 25px rgba(24, 119, 242, 0.3);
}

.empty-state h3 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 2rem 0;
  color: var(--text-color-secondary);
  line-height: 1.6;
}

/* Connected Accounts */
.connected-accounts {
  margin-bottom: 1.5rem;
}

.accounts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.accounts-header h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.accounts-header h4 i {
  color: var(--primary-color);
  font-size: 0.9rem;
}

.accounts-list {
  background: var(--surface-50);
  border-radius: 8px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.no-accounts {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--text-color-secondary);
  font-style: italic;
}

.no-accounts i {
  font-size: 1.2rem;
  opacity: 0.7;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--surface-border);
  transition: background-color 0.2s ease;
}

.account-item:last-child {
  border-bottom: none;
}

.account-item:hover {
  background: var(--surface-100);
}

.account-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.account-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-text));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.account-details {
  flex: 1;
}

.account-name {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.account-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.token-date {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.account-actions {
  flex-shrink: 0;
}

/* App Actions */
.app-actions {
  display: flex;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.app-actions .p-button {
  flex: 1;
}

/* Dialog Styles */
.app-dialog,
.token-dialog {
  width: 90vw;
  max-width: 650px;
}

.dialog-content {
  padding: 0;
}

/* Dialog Header Info */
.dialog-info {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #feecf9 0%, #f8f4ff 100%);
  border-radius: 12px;
  margin-bottom: 2rem;
  border: 1px solid rgba(85, 33, 190, 0.1);
}

.dialog-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1877f2, #166fe5);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
}

.dialog-description {
  flex: 1;
}

.dialog-description p {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  line-height: 1.5;
}

.help-links {
  display: flex;
  gap: 1rem;
}

.help-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.help-link:hover {
  color: var(--primary-color-text);
  transform: translateY(-1px);
}

/* Form Container */
.form-container {
  padding: 0 1.5rem;
}

.form-group {
  margin-bottom: 2rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.95rem;
}

.form-label i {
  color: var(--primary-color);
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--surface-border);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  background: var(--surface-0);
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(85, 33, 190, 0.1);
  outline: none;
}

.form-input::placeholder {
  color: var(--text-color-secondary);
  opacity: 0.7;
}



.form-help {
  display: block;
  margin-top: 0.5rem;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Checkbox Group */
.checkbox-group {
  margin-bottom: 1rem;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
  border: 1px solid var(--surface-border);
  transition: all 0.2s ease;
}

.checkbox-wrapper:hover {
  background: var(--surface-100);
  border-color: var(--primary-color);
}

.form-checkbox {
  margin-top: 0.125rem;
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.checkbox-label {
  flex: 1;
  margin: 0;
  cursor: pointer;
}

.checkbox-text {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.checkbox-help {
  display: block;
  color: var(--text-color-secondary);
  font-size: 0.85rem;
  line-height: 1.3;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--surface-border);
  background: var(--surface-50);
  margin: 2rem -1.5rem -1.5rem -1.5rem;
  border-radius: 0 0 12px 12px;
}

/* Token Dialog Specific Styles */
.app-info {
  margin-top: 0.75rem;
}

.app-id-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(85, 33, 190, 0.1);
  color: var(--primary-color);
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

.login-section,
.tokens-section {
  margin-bottom: 2rem;
  padding: 0 1.5rem 2rem 1.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.tokens-section {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-header {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--surface-50);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.section-header h3 {
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.section-header h3 i {
  color: var(--primary-color);
  font-size: 1rem;
}

.section-header p {
  margin: 0;
  color: var(--text-color-secondary);
  line-height: 1.4;
}

/* Login Actions */
.login-actions {
  margin-bottom: 1.5rem;
}

/* Facebook Login Button */
.facebook-login-btn {
  background: linear-gradient(135deg, #1877f2, #166fe5) !important;
  border: none !important;
  padding: 0.875rem 2rem !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3) !important;
  transition: all 0.2s ease !important;
}

.facebook-login-btn:hover {
  background: linear-gradient(135deg, #166fe5, #1460d1) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 16px rgba(24, 119, 242, 0.4) !important;
}

.facebook-login-btn:active {
  transform: translateY(0) !important;
}

/* User Info */
.user-info {
  margin-top: 1.5rem;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 2px solid rgba(24, 119, 242, 0.1);
  box-shadow: 0 2px 8px rgba(24, 119, 242, 0.1);
}

.user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #1877f2;
  box-shadow: 0 2px 8px rgba(24, 119, 242, 0.2);
}

.user-details h4 {
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
  font-weight: 600;
  font-size: 1.1rem;
}

.user-details p {
  margin: 0 0 0.25rem 0;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.user-id {
  font-family: 'Courier New', monospace;
  background: rgba(24, 119, 242, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #1877f2;
  font-weight: 500;
}

/* Pages Section */
.pages-section {
  margin-top: 1.5rem;
}

.pages-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pages-list {
  display: grid;
  gap: 0.75rem;
}

.page-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid var(--surface-border);
  transition: all 0.2s ease;
}

.page-item:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-icon {
  color: #1877f2;
  font-size: 1.1rem;
}

.page-name {
  flex: 1;
  font-weight: 500;
  color: var(--text-color);
}

/* Tokens List */
.empty-tokens {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-color-secondary);
  background: var(--surface-50);
  border-radius: 12px;
  border: 2px dashed var(--surface-border);
}

.empty-tokens i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.tokens-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.token-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.token-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.token-info {
  flex: 1;
}

.token-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.token-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.token-preview {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  display: inline-block;
  border: 1px solid var(--surface-border);
  color: var(--text-color);
  font-weight: 500;
}

.token-date {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.token-actions {
  flex-shrink: 0;
  margin-left: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fb-accounts-manager {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  /* Apps Grid Responsive */
  .apps-grid,
  .loading-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .app-card {
    padding: 1.25rem;
  }

  .app-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .app-info {
    justify-content: center;
    text-align: center;
  }

  .app-status {
    align-self: center;
  }

  .app-meta {
    flex-direction: column;
    gap: 0.75rem;
  }

  .accounts-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .account-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    text-align: center;
  }

  .account-info {
    justify-content: center;
  }

  .account-meta {
    justify-content: center;
  }

  .account-actions {
    align-self: center;
  }

  .app-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  /* Dialog Responsive */
  .app-dialog,
  .token-dialog {
    width: 95vw;
    max-width: none;
  }

  .dialog-info {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .dialog-icon {
    align-self: center;
  }

  .form-container {
    padding: 0 1rem;
  }

  .login-section,
  .tokens-section {
    padding: 0 1rem 2rem 1rem;
  }

  .user-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .token-item {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .token-header {
    justify-content: center;
  }

  .token-actions {
    margin-left: 0;
    align-self: center;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 0.75rem;
  }

  .dialog-footer .p-button {
    width: 100%;
  }
}

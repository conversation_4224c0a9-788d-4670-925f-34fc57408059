import { Injectable } from '@angular/core';
import { Observable, from, map, catchError, throwError } from 'rxjs';
import { SupabaseService } from '../../../core/services/supabase.service';
import { 
  FacebookAccountsStructure, 
  FacebookAppWithUsers, 
  FacebookUserWithAccounts,
  FacebookAdAccount,
  SelectedFacebookAccount 
} from '../types/facebook-accounts-structure.types';

@Injectable({
  providedIn: 'root',
})
export class FacebookAccountsStructureService {
  constructor(private supabaseService: SupabaseService) {}

  /**
   * Get complete Facebook accounts structure (apps → users → ad accounts)
   * This calls the Edge Function which handles Graph API calls securely
   */
  getAccountsStructure(): Observable<FacebookAccountsStructure> {
    return from(
      this.supabaseService.client.functions.invoke('facebook-accounts-structure', {
        body: {}
      })
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('Error from Edge Function:', response.error);
          throw response.error;
        }
        return response.data as FacebookAccountsStructure;
      }),
      catchError((error) => {
        console.error('Error fetching Facebook accounts structure:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Get flattened list of all available ad accounts for feature configuration
   */
  getAvailableAdAccountsFlat(): Observable<Array<{
    account: FacebookAdAccount;
    app: { id: string; app_id: string; name: string };
    user: { id: string; facebook_user_id: string; name: string; profile_picture_url?: string };
  }>> {
    return this.getAccountsStructure().pipe(
      map(structure => {
        const flatAccounts: Array<{
          account: FacebookAdAccount;
          app: { id: string; app_id: string; name: string };
          user: { id: string; facebook_user_id: string; name: string; profile_picture_url?: string };
        }> = [];

        structure.apps.forEach(appData => {
          appData.users.forEach(userData => {
            userData.adAccounts.forEach(account => {
              flatAccounts.push({
                account,
                app: {
                  id: appData.app.id,
                  app_id: appData.app.app_id,
                  name: appData.app.name
                },
                user: {
                  id: userData.user.id,
                  facebook_user_id: userData.user.facebook_user_id,
                  name: userData.user.name,
                  profile_picture_url: userData.user.profile_picture_url
                }
              });
            });
          });
        });

        return flatAccounts;
      })
    );
  }

  /**
   * Get accounts structure filtered by active accounts only
   */
  getActiveAccountsStructure(): Observable<FacebookAccountsStructure> {
    return this.getAccountsStructure().pipe(
      map(structure => ({
        apps: structure.apps.map(appData => ({
          ...appData,
          users: appData.users.map(userData => ({
            ...userData,
            adAccounts: userData.adAccounts.filter(account => account.account_status === 1) // Active only
          })).filter(userData => userData.adAccounts.length > 0) // Remove users with no active accounts
        })).filter(appData => appData.users.length > 0) // Remove apps with no users with active accounts
      }))
    );
  }

  /**
   * Validate selected accounts against current structure
   */
  validateSelectedAccounts(selectedAccounts: SelectedFacebookAccount[]): Observable<{
    valid: SelectedFacebookAccount[];
    invalid: SelectedFacebookAccount[];
    missing: string[];
  }> {
    return this.getAccountsStructure().pipe(
      map(structure => {
        const valid: SelectedFacebookAccount[] = [];
        const invalid: SelectedFacebookAccount[] = [];
        const missing: string[] = [];

        selectedAccounts.forEach(selected => {
          const app = structure.apps.find(a => a.app.app_id === selected.appId);
          if (!app) {
            invalid.push(selected);
            missing.push(`App ${selected.appName} not found`);
            return;
          }

          const user = app.users.find(u => u.user.id === selected.userId);
          if (!user) {
            invalid.push(selected);
            missing.push(`User ${selected.userName} not found in app ${selected.appName}`);
            return;
          }

          const availableAccountIds = user.adAccounts.map(acc => acc.id);
          const validAccountIds = selected.adAccountIds.filter(id => availableAccountIds.includes(id));
          
          if (validAccountIds.length === 0) {
            invalid.push(selected);
            missing.push(`No valid ad accounts found for user ${selected.userName}`);
          } else {
            valid.push({
              ...selected,
              adAccountIds: validAccountIds
            });
            
            if (validAccountIds.length < selected.adAccountIds.length) {
              const missingAccountIds = selected.adAccountIds.filter(id => !availableAccountIds.includes(id));
              missing.push(`Ad accounts ${missingAccountIds.join(', ')} no longer available for user ${selected.userName}`);
            }
          }
        });

        return { valid, invalid, missing };
      })
    );
  }

  /**
   * Get summary statistics
   */
  getAccountsSummary(): Observable<{
    totalApps: number;
    totalUsers: number;
    totalAdAccounts: number;
    activeAdAccounts: number;
    expiredUsers: number;
  }> {
    return this.getAccountsStructure().pipe(
      map(structure => {
        let totalUsers = 0;
        let totalAdAccounts = 0;
        let activeAdAccounts = 0;
        let expiredUsers = 0;

        structure.apps.forEach(appData => {
          appData.users.forEach(userData => {
            totalUsers++;
            
            // Check if user token is expired
            if (userData.user.expires_at) {
              const expiryDate = new Date(userData.user.expires_at);
              if (expiryDate < new Date()) {
                expiredUsers++;
              }
            }

            userData.adAccounts.forEach(account => {
              totalAdAccounts++;
              if (account.account_status === 1) {
                activeAdAccounts++;
              }
            });
          });
        });

        return {
          totalApps: structure.apps.length,
          totalUsers,
          totalAdAccounts,
          activeAdAccounts,
          expiredUsers
        };
      })
    );
  }
}

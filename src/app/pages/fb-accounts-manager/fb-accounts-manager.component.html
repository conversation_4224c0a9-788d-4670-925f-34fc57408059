<div class="page-container">
  <div class="page-header">
    <div class="header-content">
      <div class="header-info">
        <h1 class="page-title">
          <i class="fab fa-facebook-f"></i>
          Facebook Accounts Manager
        </h1>
        <p class="page-description">
          Manage your Facebook applications and authentication tokens
        </p>
      </div>
    </div>
  </div>

  <div class="content-container">
    <!-- Facebook Apps Cards -->
    <div class="apps-section">
      <div class="section-header">
        <div class="section-title">
          <h2>Facebook Applications</h2>
          <p>Manage your Facebook apps and connected accounts</p>
        </div>
        <div class="section-actions">
          <p-button
            (onClick)="openInstructionsDialog()"
            icon="pi pi-info-circle"
            label="App Creation Instructions"
            severity="secondary"
            [outlined]="true">
          </p-button>
          <p-button
            (onClick)="openAppDialog()"
            icon="pi pi-plus"
            label="Add Facebook App"
            severity="primary">
          </p-button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-grid">
        <div *ngFor="let item of [1,2,3]" class="app-card-skeleton">
          <p-skeleton height="200px" borderRadius="12px"></p-skeleton>
        </div>
      </div>

      <!-- Apps Grid -->
      <div *ngIf="!loading && facebookApps.length > 0" class="apps-grid">
        <div *ngFor="let app of facebookApps" class="app-card">
          <!-- App Header -->
          <div class="app-header">
            <div class="app-info">
              <div class="app-icon-wrapper">
                <i class="fab fa-facebook-f"></i>
              </div>
              <div class="app-details">
                <h3 class="app-name">{{ app.name }}</h3>
                <code class="app-id">{{ app.app_id }}</code>
              </div>
            </div>
            <div class="app-status">
              <p-tag
                [value]="app.is_active ? 'Active' : 'Inactive'"
                [severity]="app.is_active ? 'success' : 'danger'"
                size="small">
              </p-tag>
            </div>
          </div>

          <!-- App Meta -->
          <div class="app-meta">
            <div class="meta-item">
              <i class="pi pi-calendar"></i>
              <span>Created {{ formatDate(app.created_at!) }}</span>
            </div>
            <div class="meta-item">
              <i class="pi pi-users"></i>
              <span>{{ getTokenCount(app.id!) }} users</span>
            </div>
            <div *ngIf="hasExpiredTokens(app.id!)" class="meta-item expired-warning">
              <i class="pi pi-exclamation-triangle"></i>
              <span>{{ getExpiredTokenCount(app.id!) }} expired</span>
            </div>
          </div>

          <!-- Users -->
          <div class="connected-accounts">
            <div class="accounts-header">
              <h4>
                <i class="pi pi-users"></i>
                Users
              </h4>
              <p-button
                (onClick)="loginWithFacebookForApp(app)"
                [loading]="isLoggingInForApp === app.id"
                icon="fab fa-facebook-f"
                label="Connect"
                severity="primary"
                [outlined]="true">
              </p-button>
            </div>

            <!-- User Accounts List -->
            <div class="accounts-list">
              <div *ngIf="getAppTokens(app.id!).length === 0" class="no-accounts">
                <i class="pi pi-user-plus"></i>
                <span>No users connected</span>
              </div>

              <div *ngFor="let user of getAppTokens(app.id!)" class="account-item">
                <div class="account-info">
                  <div class="account-avatar">
                    <img
                      *ngIf="user.profile_picture_url; else defaultAvatar"
                      [src]="user.profile_picture_url"
                      [alt]="user.name"
                      class="profile-picture">
                    <ng-template #defaultAvatar>
                      <i class="pi pi-user"></i>
                    </ng-template>
                  </div>
                  <div class="account-details">
                    <span class="account-name">
                      {{ user.name }}
                    </span>
                    <div class="account-meta">
                      <p-tag
                        value="Connected"
                        severity="success"
                        size="small">
                      </p-tag>
                      <p-tag
                        *ngIf="user.expires_at"
                        [value]="'Expires: ' + getTimeUntilExpiration(user.expires_at)"
                        [severity]="getExpirationSeverity(user.expires_at)"
                        size="small">
                      </p-tag>
                      <span class="token-date">Last login: {{ formatDate(user.last_login || user.created_at!) }}</span>
                    </div>
                  </div>
                </div>
                <div class="account-actions">
                  <p-button
                    (onClick)="deleteToken(user)"
                    icon="pi pi-trash"
                    severity="danger"
                    size="small"
                    [text]="true"
                    styleClass="action-button"
                    pTooltip="Remove User">
                  </p-button>
                </div>
              </div>
            </div>
          </div>

          <!-- App Actions -->
          <div class="app-actions">
            <p-button
              (onClick)="openAppDialog(app)"
              icon="pi pi-pencil"
              label="Edit"
              severity="secondary"
              [outlined]="true">
            </p-button>
            <p-button
              (onClick)="deleteApp(app)"
              icon="pi pi-trash"
              label="Delete"
              severity="danger"
              [outlined]="true">
            </p-button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && facebookApps.length === 0" class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">
            <i class="fab fa-facebook-f"></i>
          </div>
          <h3>No Facebook Apps</h3>
          <p>Add your first Facebook application to get started with social media management</p>
          <p-button
            (onClick)="openAppDialog()"
            icon="pi pi-plus"
            label="Add Facebook App"
            severity="primary">
          </p-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add/Edit App Dialog -->
  <p-dialog
    [(visible)]="showAppDialog"
    [modal]="true"
    [closable]="true"
    [draggable]="false"
    [resizable]="false"
    styleClass="app-dialog"
    [header]="editingApp ? 'Edit Facebook App' : 'Add Facebook App'">

    <div class="dialog-content">
      <!-- Dialog Header Info -->
      <div class="dialog-info">
        <div class="dialog-icon">
          <i class="fab fa-facebook-f"></i>
        </div>
        <div class="dialog-description">
          <p>Configure your Facebook application credentials to enable Facebook integration and token management.</p>
          <div class="help-links">
            <a href="https://developers.facebook.com/apps/" target="_blank" class="help-link">
              <i class="pi pi-external-link"></i>
              Facebook Developer Console
            </a>
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="form-container">
        <div class="form-group">
          <label for="appName" class="form-label">
            <i class="pi pi-tag"></i>
            App Name *
          </label>
          <input
            id="appName"
            type="text"
            pInputText
            [(ngModel)]="appForm.name"
            placeholder="e.g., My Company Facebook App"
            class="form-input" />
          <small class="form-help">A friendly name to identify this Facebook app</small>
        </div>

        <div class="form-group">
          <label for="appId" class="form-label">
            <i class="pi pi-id-card"></i>
            App ID *
          </label>
          <input
            id="appId"
            type="text"
            pInputText
            [(ngModel)]="appForm.app_id"
            placeholder="1234567890123456"
            class="form-input" />
          <small class="form-help">Your Facebook App ID from the Developer Console</small>
        </div>

        <div class="form-group">
          <label for="appSecret" class="form-label">
            App Secret *
          </label>
          <input
            id="appSecret"
            type="password"
            pInputText
            [(ngModel)]="appForm.app_secret"
            placeholder="Enter your Facebook App Secret"
            class="form-input" />
          <small class="form-help">Your Facebook App Secret (keep this secure!)</small>
        </div>

        <div class="form-group checkbox-group">
          <div class="checkbox-wrapper">
            <input
              id="isActive"
              type="checkbox"
              [(ngModel)]="appForm.is_active"
              class="form-checkbox" />
            <label for="isActive" class="checkbox-label">
              <span class="checkbox-text">Active</span>
              <small class="checkbox-help">Enable this app for token generation</small>
            </label>
          </div>
        </div>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <div class="dialog-footer">
        <p-button
          (onClick)="closeAppDialog()"
          label="Cancel"
          severity="secondary"
          [outlined]="true"
          icon="pi pi-times">
        </p-button>
        <p-button
          (onClick)="saveApp()"
          [label]="editingApp ? 'Update App' : 'Create App'"
          severity="primary"
          [icon]="editingApp ? 'pi pi-check' : 'pi pi-plus'">
        </p-button>
      </div>
    </ng-template>
  </p-dialog>



  <!-- Toast Messages -->
  <p-toast></p-toast>
  
  <!-- Confirmation Dialog -->
  <p-confirmDialog></p-confirmDialog>
</div>

<div class="fb-accounts-manager">
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">
          <i class="fab fa-facebook-f"></i>
          Facebook Accounts Manager
        </h1>
        <p class="page-subtitle">
          Manage your Facebook applications and authentication tokens
        </p>
      </div>
      <div class="header-actions">
        <p-button
          (onClick)="openAppDialog()"
          icon="pi pi-plus"
          label="Add Facebook App"
          severity="primary">
        </p-button>
      </div>
    </div>
  </div>

  <div class="content-container">
    <!-- Facebook Apps Table -->
    <p-card header="Facebook Applications" styleClass="apps-card">
      <p-table
        [value]="facebookApps"
        [loading]="loading"
        styleClass="p-datatable-sm"
        responsiveLayout="scroll">
        
        <ng-template pTemplate="header">
          <tr>
            <th>Name</th>
            <th>App ID</th>
            <th>Status</th>
            <th>Created</th>
            <th>Actions</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-app>
          <tr>
            <td>
              <div class="app-name">
                <i class="fab fa-facebook-f app-icon"></i>
                <span>{{ app.name }}</span>
              </div>
            </td>
            <td>
              <code class="app-id">{{ app.app_id }}</code>
            </td>
            <td>
              <p-tag
                [value]="app.is_active ? 'Active' : 'Inactive'"
                [severity]="app.is_active ? 'success' : 'danger'">
              </p-tag>
            </td>
            <td>{{ formatDate(app.created_at!) }}</td>
            <td>
              <div class="action-buttons">
                <p-button
                  (onClick)="openTokenDialog(app)"
                  icon="pi pi-key"
                  severity="info"
                  size="small"
                  [outlined]="true"
                  pTooltip="Manage Tokens">
                </p-button>
                <p-button
                  (onClick)="openAppDialog(app)"
                  icon="pi pi-pencil"
                  severity="secondary"
                  size="small"
                  [outlined]="true"
                  pTooltip="Edit App">
                </p-button>
                <p-button
                  (onClick)="deleteApp(app)"
                  icon="pi pi-trash"
                  severity="danger"
                  size="small"
                  [outlined]="true"
                  pTooltip="Delete App">
                </p-button>
              </div>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="5" class="empty-message">
              <div class="empty-state">
                <i class="fab fa-facebook-f empty-icon"></i>
                <h3>No Facebook Apps</h3>
                <p>Add your first Facebook application to get started</p>
                <p-button
                  (onClick)="openAppDialog()"
                  icon="pi pi-plus"
                  label="Add Facebook App"
                  severity="primary">
                </p-button>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </p-card>
  </div>

  <!-- Add/Edit App Dialog -->
  <p-dialog
    [(visible)]="showAppDialog"
    [modal]="true"
    [closable]="true"
    [draggable]="false"
    [resizable]="false"
    styleClass="app-dialog"
    [header]="editingApp ? 'Edit Facebook App' : 'Add Facebook App'">

    <div class="dialog-content">
      <!-- Dialog Header Info -->
      <div class="dialog-info">
        <div class="dialog-icon">
          <i class="fab fa-facebook-f"></i>
        </div>
        <div class="dialog-description">
          <p>Configure your Facebook application credentials to enable Facebook integration and token management.</p>
          <div class="help-links">
            <a href="https://developers.facebook.com/apps/" target="_blank" class="help-link">
              <i class="pi pi-external-link"></i>
              Facebook Developer Console
            </a>
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="form-container">
        <div class="form-group">
          <label for="appName" class="form-label">
            <i class="pi pi-tag"></i>
            App Name *
          </label>
          <input
            id="appName"
            type="text"
            pInputText
            [(ngModel)]="appForm.name"
            placeholder="e.g., My Company Facebook App"
            class="form-input" />
          <small class="form-help">A friendly name to identify this Facebook app</small>
        </div>

        <div class="form-group">
          <label for="appId" class="form-label">
            <i class="pi pi-id-card"></i>
            App ID *
          </label>
          <input
            id="appId"
            type="text"
            pInputText
            [(ngModel)]="appForm.app_id"
            placeholder="1234567890123456"
            class="form-input" />
          <small class="form-help">Your Facebook App ID from the Developer Console</small>
        </div>

        <div class="form-group">
          <label for="appSecret" class="form-label">
            <i class="pi pi-key"></i>
            App Secret *
          </label>
          <div class="password-field">
            <p-password
              id="appSecret"
              [(ngModel)]="appForm.app_secret"
              placeholder="Enter your Facebook App Secret"
              [toggleMask]="true"
              [feedback]="false"
              inputStyleClass="form-input">
            </p-password>
          </div>
          <small class="form-help">Your Facebook App Secret (keep this secure!)</small>
        </div>

        <div class="form-group checkbox-group">
          <div class="checkbox-wrapper">
            <input
              id="isActive"
              type="checkbox"
              [(ngModel)]="appForm.is_active"
              class="form-checkbox" />
            <label for="isActive" class="checkbox-label">
              <span class="checkbox-text">Active</span>
              <small class="checkbox-help">Enable this app for token generation</small>
            </label>
          </div>
        </div>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <div class="dialog-footer">
        <p-button
          (onClick)="closeAppDialog()"
          label="Cancel"
          severity="secondary"
          [outlined]="true"
          icon="pi pi-times">
        </p-button>
        <p-button
          (onClick)="saveApp()"
          [label]="editingApp ? 'Update App' : 'Create App'"
          severity="primary"
          [icon]="editingApp ? 'pi pi-check' : 'pi pi-plus'">
        </p-button>
      </div>
    </ng-template>
  </p-dialog>

  <!-- Token Management Dialog -->
  <p-dialog
    [(visible)]="showTokenDialog"
    [modal]="true"
    [closable]="true"
    [draggable]="false"
    [resizable]="false"
    styleClass="token-dialog"
    [header]="'Manage Tokens - ' + (selectedApp?.name || '')">

    <div class="dialog-content">
      <!-- Dialog Header Info -->
      <div class="dialog-info">
        <div class="dialog-icon">
          <i class="pi pi-key"></i>
        </div>
        <div class="dialog-description">
          <p>Authenticate with Facebook to generate and manage access tokens for <strong>{{ selectedApp?.name }}</strong>.</p>
          <div class="app-info">
            <span class="app-id-badge">
              <i class="pi pi-id-card"></i>
              App ID: {{ selectedApp?.app_id }}
            </span>
          </div>
        </div>
      </div>
      <!-- Facebook Login Section -->
      <div class="login-section">
        <div class="section-header">
          <h3>
            <i class="fab fa-facebook-f"></i>
            Facebook Authentication
          </h3>
          <p>Login with Facebook to generate and save access tokens</p>
        </div>
        
        <div class="login-actions">
          <p-button
            (onClick)="loginWithFacebook()"
            [loading]="isLoggingIn"
            icon="fab fa-facebook-f"
            label="Login with Facebook"
            severity="primary"
            styleClass="facebook-login-btn">
          </p-button>
        </div>

        <!-- User Info Display -->
        <div *ngIf="userInfo" class="user-info">
          <div class="user-card">
            <img
              *ngIf="userInfo.picture"
              [src]="userInfo.picture.data.url"
              [alt]="userInfo.name"
              class="user-avatar" />
            <div class="user-details">
              <h4>{{ userInfo.name }}</h4>
              <p *ngIf="userInfo.email">{{ userInfo.email }}</p>
              <p class="user-id">ID: {{ userInfo.id }}</p>
            </div>
          </div>
        </div>

        <!-- Pages Display -->
        <div *ngIf="userPages.length > 0" class="pages-section">
          <h4>Connected Pages ({{ userPages.length }})</h4>
          <div class="pages-list">
            <div *ngFor="let page of userPages" class="page-item">
              <i class="fab fa-facebook-f page-icon"></i>
              <span class="page-name">{{ page.name }}</span>
              <p-tag value="Page" severity="success" size="small"></p-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- Saved Tokens Section -->
      <div class="tokens-section">
        <div class="section-header">
          <h3>
            <i class="pi pi-key"></i>
            Saved Tokens
          </h3>
          <p>Manage your saved Facebook access tokens</p>
        </div>

        <div *ngIf="tokens.length === 0" class="empty-tokens">
          <i class="pi pi-key empty-icon"></i>
          <p>No tokens saved yet. Login with Facebook to generate tokens.</p>
        </div>

        <div *ngIf="tokens.length > 0" class="tokens-list">
          <div *ngFor="let token of tokens" class="token-item">
            <div class="token-info">
              <div class="token-header">
                <p-tag
                  [value]="getTokenTypeLabel(token.token_type)"
                  [severity]="getTokenTypeSeverity(token.token_type)"
                  size="small">
                </p-tag>
                <span *ngIf="token.page_name" class="page-name">{{ token.page_name }}</span>
                <p-tag
                  *ngIf="isTokenExpired(token)"
                  value="Expired"
                  severity="danger"
                  size="small">
                </p-tag>
              </div>
              <div class="token-details">
                <code class="token-preview">{{ token.access_token.substring(0, 20) }}...</code>
                <span class="token-date">Created: {{ formatDate(token.created_at!) }}</span>
              </div>
            </div>
            <div class="token-actions">
              <p-button
                (onClick)="deleteToken(token)"
                icon="pi pi-trash"
                severity="danger"
                size="small"
                [outlined]="true"
                pTooltip="Delete Token">
              </p-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <div class="dialog-footer">
        <p-button
          (onClick)="closeTokenDialog()"
          label="Close"
          severity="secondary">
        </p-button>
      </div>
    </ng-template>
  </p-dialog>

  <!-- Toast Messages -->
  <p-toast></p-toast>
  
  <!-- Confirmation Dialog -->
  <p-confirmDialog></p-confirmDialog>
</div>

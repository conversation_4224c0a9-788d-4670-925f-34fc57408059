import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// PrimeNG imports
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MessageService, ConfirmationService } from 'primeng/api';

import { FacebookAppService } from './services';
import {
  FacebookApp,
  FacebookToken,
  FacebookLoginResponse,
  FacebookUserInfo,
  FacebookPage,
} from './models';

@Component({
  selector: 'chm-fb-accounts-manager',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CardModule,
    InputTextModule,
    PasswordModule,
    DialogModule,
    TableModule,
    TagModule,
    ToastModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './fb-accounts-manager.component.html',
  styleUrls: ['./fb-accounts-manager.component.css'],
})
export class FbAccountsManagerComponent implements OnInit, OnDestroy {
  // Data
  facebookApps: FacebookApp[] = [];
  loading = false;
  
  // App Management
  showAppDialog = false;
  editingApp: FacebookApp | null = null;
  appForm: Partial<FacebookApp> = {};
  
  // Token Management
  showTokenDialog = false;
  selectedApp: FacebookApp | null = null;
  tokens: FacebookToken[] = [];
  
  // Facebook Login
  isLoggingIn = false;
  userInfo: FacebookUserInfo | null = null;
  userPages: FacebookPage[] = [];

  private destroy$ = new Subject<void>();

  constructor(
    private facebookAppService: FacebookAppService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
  ) {}

  ngOnInit(): void {
    this.loadFacebookApps();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // App Management Methods
  loadFacebookApps(): void {
    this.loading = true;
    this.facebookAppService
      .getFacebookApps()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.facebookApps = response.data;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading Facebook apps:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load Facebook apps',
          });
          this.loading = false;
        },
      });
  }

  openAppDialog(app?: FacebookApp): void {
    this.editingApp = app || null;
    this.appForm = app ? { ...app } : { name: '', app_id: '', app_secret: '', is_active: true };
    this.showAppDialog = true;
  }

  closeAppDialog(): void {
    this.showAppDialog = false;
    this.editingApp = null;
    this.appForm = {};
  }

  saveApp(): void {
    if (!this.appForm.name || !this.appForm.app_id || !this.appForm.app_secret) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields',
      });
      return;
    }

    const operation = this.editingApp
      ? this.facebookAppService.updateFacebookApp(this.editingApp.id!, this.appForm)
      : this.facebookAppService.createFacebookApp(this.appForm as Omit<FacebookApp, 'id'>);

    operation.pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: `Facebook app ${this.editingApp ? 'updated' : 'created'} successfully`,
        });
        this.closeAppDialog();
        this.loadFacebookApps();
      },
      error: (error) => {
        console.error('Error saving Facebook app:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: `Failed to ${this.editingApp ? 'update' : 'create'} Facebook app`,
        });
      },
    });
  }

  deleteApp(app: FacebookApp): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete the Facebook app "${app.name}"?`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.facebookAppService
          .deleteFacebookApp(app.id!)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Facebook app deleted successfully',
              });
              this.loadFacebookApps();
            },
            error: (error) => {
              console.error('Error deleting Facebook app:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete Facebook app',
              });
            },
          });
      },
    });
  }

  // Token Management Methods
  async openTokenDialog(app: FacebookApp): Promise<void> {
    this.selectedApp = app;
    this.showTokenDialog = true;
    this.tokens = [];
    this.userInfo = null;
    this.userPages = [];

    // Initialize Facebook SDK for this app
    try {
      await this.facebookAppService.initializeFacebookSDK(app.app_id);
      this.loadTokensForApp(app.id!);
    } catch (error) {
      console.error('Error initializing Facebook SDK:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to initialize Facebook SDK',
      });
    }
  }

  closeTokenDialog(): void {
    this.showTokenDialog = false;
    this.selectedApp = null;
    this.tokens = [];
    this.userInfo = null;
    this.userPages = [];
  }

  loadTokensForApp(appId: string): void {
    this.facebookAppService
      .getTokensForApp(appId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tokens) => {
          this.tokens = tokens;
        },
        error: (error) => {
          console.error('Error loading tokens:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load tokens',
          });
        },
      });
  }

  async loginWithFacebook(): Promise<void> {
    if (!this.selectedApp) return;

    this.isLoggingIn = true;
    try {
      // Login with Facebook
      const loginResponse: FacebookLoginResponse = await this.facebookAppService.loginWithFacebook();
      
      // Get user info
      this.userInfo = await this.facebookAppService.getUserInfo(loginResponse.accessToken);
      
      // Get user pages
      this.userPages = await this.facebookAppService.getUserPages(loginResponse.accessToken);

      // Extend the access token
      const longLivedToken = await this.facebookAppService.extendAccessToken(
        loginResponse.accessToken,
        this.selectedApp.app_id,
        this.selectedApp.app_secret,
      );

      // Save the long-lived user token
      const userToken: Omit<FacebookToken, 'id'> = {
        app_id: this.selectedApp.app_id,
        access_token: longLivedToken,
        token_type: 'user',
        is_long_lived: true,
        permissions: ['email', 'pages_read_engagement', 'pages_manage_posts', 'ads_read', 'ads_management'],
        user_id: this.userInfo.id,
      };

      await this.facebookAppService.saveToken(userToken).toPromise();

      // Save page tokens
      for (const page of this.userPages) {
        const pageToken: Omit<FacebookToken, 'id'> = {
          app_id: this.selectedApp.app_id,
          access_token: page.access_token,
          token_type: 'page',
          is_long_lived: true,
          user_id: this.userInfo.id,
          page_id: page.id,
          page_name: page.name,
        };

        await this.facebookAppService.saveToken(pageToken).toPromise();
      }

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Successfully logged in and saved tokens',
      });

      // Reload tokens
      this.loadTokensForApp(this.selectedApp.id!);

    } catch (error) {
      console.error('Error during Facebook login:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to login with Facebook',
      });
    } finally {
      this.isLoggingIn = false;
    }
  }

  deleteToken(token: FacebookToken): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this token?',
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.facebookAppService
          .deleteToken(token.id!)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Token deleted successfully',
              });
              this.loadTokensForApp(this.selectedApp!.id!);
            },
            error: (error) => {
              console.error('Error deleting token:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete token',
              });
            },
          });
      },
    });
  }

  // Utility Methods
  getTokenTypeLabel(type: string): string {
    const labels: { [key: string]: string } = {
      user: 'User Token',
      page: 'Page Token',
      app: 'App Token',
    };
    return labels[type] || type;
  }

  getTokenTypeSeverity(type: string): string {
    const severities: { [key: string]: string } = {
      user: 'info',
      page: 'success',
      app: 'warning',
    };
    return severities[type] || 'info';
  }

  isTokenExpired(token: FacebookToken): boolean {
    if (!token.expires_at) return false;
    return new Date(token.expires_at) < new Date();
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }
}

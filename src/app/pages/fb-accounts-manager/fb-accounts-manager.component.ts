import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmationService, MessageService } from 'primeng/api';

import { FacebookAppService } from './services';
import {
  FacebookApp,
  FacebookLoginResponse,
  FacebookPage,
  FacebookToken,
  FacebookUser,
  FacebookUserInfo,
} from './models';

@Component({
  selector: 'chm-fb-accounts-manager',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CardModule,
    InputTextModule,
    PasswordModule,
    DialogModule,
    TableModule,
    TagModule,
    ToastModule,
    ConfirmDialogModule,
    SkeletonModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './fb-accounts-manager.component.html',
  styleUrls: ['./fb-accounts-manager.component.css'],
})
export class FbAccountsManagerComponent implements OnInit, OnDestroy {
  // Data
  facebookApps: FacebookApp[] = [];
  allTokens: FacebookToken[] = [];
  loading = false;

  // App Management
  showAppDialog = false;
  editingApp: FacebookApp | null = null;
  appForm: Partial<FacebookApp> = {};

  // Facebook Login
  isLoggingInForApp: string | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private facebookAppService: FacebookAppService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
  ) {}

  ngOnInit(): void {
    this.loadFacebookApps();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // App Management Methods
  loadFacebookApps(): void {
    this.loading = true;
    this.facebookAppService
      .getFacebookApps()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.facebookApps = response.data;
          // Load all tokens after apps are loaded
          this.loadAllTokens();
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading Facebook apps:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load Facebook apps',
          });
          this.loading = false;
        },
      });
  }

  openAppDialog(app?: FacebookApp): void {
    this.editingApp = app || null;
    this.appForm = app
      ? { ...app }
      : { name: '', app_id: '', app_secret: '', is_active: true };
    this.showAppDialog = true;
  }

  closeAppDialog(): void {
    this.showAppDialog = false;
    this.editingApp = null;
    this.appForm = {};
  }

  saveApp(): void {
    if (
      !this.appForm.name ||
      !this.appForm.app_id ||
      !this.appForm.app_secret
    ) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields',
      });
      return;
    }

    const operation = this.editingApp
      ? this.facebookAppService.updateFacebookApp(
          this.editingApp.id!,
          this.appForm,
        )
      : this.facebookAppService.createFacebookApp(
          this.appForm as Omit<FacebookApp, 'id'>,
        );

    operation.pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: `Facebook app ${this.editingApp ? 'updated' : 'created'} successfully`,
        });
        this.closeAppDialog();
        this.loadFacebookApps();
      },
      error: (error) => {
        console.error('Error saving Facebook app:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: `Failed to ${this.editingApp ? 'update' : 'create'} Facebook app`,
        });
      },
    });
  }

  deleteApp(app: FacebookApp): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete the Facebook app "${app.name}"?`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.facebookAppService
          .deleteFacebookApp(app.id!)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Facebook app deleted successfully',
              });
              this.loadFacebookApps();
            },
            error: (error) => {
              console.error('Error deleting Facebook app:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete Facebook app',
              });
            },
          });
      },
    });
  }





  deleteToken(token: FacebookToken): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this token?',
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.facebookAppService
          .deleteToken(token.id!)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'User removed successfully',
              });
              this.loadAllTokens();
            },
            error: (error) => {
              console.error('Error deleting token:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete token',
              });
            },
          });
      },
    });
  }

  // Utility Methods
  isTokenExpired(token: FacebookToken): boolean {
    if (!token.expires_at) return false;
    return new Date(token.expires_at) < new Date();
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  // New methods for card-based design
  loadAllTokens(): void {
    // Get all tokens with a single query
    this.facebookAppService
      .getAllTokens()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tokens) => {
          this.allTokens = tokens;
        },
        error: (error) => {
          console.error('Error loading all tokens:', error);
        },
      });
  }

  getAppTokens(appId: string): FacebookToken[] {
    const app = this.facebookApps.find((a) => a.id === appId);
    if (!app) return [];
    return this.allTokens.filter((token) => token.app_id === app.app_id);
  }

  getTokenCount(appId: string): number {
    return this.getAppTokens(appId).length;
  }

  async loginWithFacebookForApp(app: FacebookApp): Promise<void> {
    this.isLoggingInForApp = app.id!;

    try {
      // Initialize Facebook SDK for this app
      await this.facebookAppService.initializeFacebookSDK(app.app_id);

      // Login with Facebook
      const loginResponse: FacebookLoginResponse =
        await this.facebookAppService.loginWithFacebook();

      // Get user info
      const userInfo = await this.facebookAppService.getUserInfo(
        loginResponse.accessToken,
      );

      // Get user pages
      const userPages = await this.facebookAppService.getUserPages(
        loginResponse.accessToken,
      );

      // Extend the access token
      const longLivedToken = await this.facebookAppService.extendAccessToken(
        loginResponse.accessToken,
        app.app_id,
        app.app_secret,
      );

      // Calculate expiration date (60 days for long-lived tokens)
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 60);

      // Save the user with profile information
      const facebookUser: Omit<FacebookUser, 'id'> = {
        app_id: app.app_id,
        access_token: longLivedToken,
        facebook_user_id: userInfo.id,
        name: userInfo.name,
        email: userInfo.email,
        profile_picture_url: userInfo.picture?.data?.url,
        is_long_lived: true,
        permissions: [
          'ads_read',
          'business_management',
          'pages_show_list',
          'ads_management',
          'pages_read_engagement',
          'pages_manage_ads',
          'pages_read_user_content',
          'pages_manage_metadata',
          'pages_manage_posts',
        ],
        expires_at: expirationDate.toISOString(),
        last_login: new Date().toISOString(),
      };

      await this.facebookAppService.saveUser(facebookUser).toPromise();

      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: `Successfully connected user account to ${app.name}`,
      });

      // Reload tokens for this app
      this.loadAllTokens();
    } catch (error) {
      console.error('Error during Facebook login:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to connect Facebook account',
      });
    } finally {
      this.isLoggingInForApp = null;
    }
  }
}

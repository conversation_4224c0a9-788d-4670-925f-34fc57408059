// Portal-level configuration (UI, routes, icons, etc.)
export interface PortalFeature {
  id: string;
  name: string;
  icon: string;
  route: string;
  roles?: string[];
  // Note: enabled flag moved to CustomerConfig
}

export interface PortalConfig {
  features: PortalFeature[];
  // Note: supabase config moved to CustomerConfig
}

// Legacy interfaces for backward compatibility
export interface SupabaseConfig {
  url: string;
  anonKey: string;
}

import type { PortalConfig } from './core/types';
import { PortalFeature } from './core/types';
import { customerConfig } from '../../customers';

const featureDefinitions = {
  [PortalFeature.AD_WINNERS]: {
    id: 'ad-winners',
    name: 'Ad Winners',
    icon: 'pi pi-trophy',
    route: '/ad-winners',
  },
  [PortalFeature.CREATIVES_UPLOADER]: {
    id: 'creatives-uploader',
    name: 'Creatives Uploader',
    icon: 'pi pi-cloud-upload',
    route: '/creatives-uploader',
  },
  [PortalFeature.FB_ACCOUNTS_MANAGER]: {
    id: 'fb-accounts-manager',
    name: 'Facebook Accounts',
    icon: 'fab fa-facebook-f',
    route: '/fb-accounts-manager',
  },
};

// Build portal config from customer config
export const portalConfig: PortalConfig = {
  supabase: customerConfig.supabase,
  features: customerConfig.enabledFeatures.map((featureId: PortalFeature) => ({
    ...featureDefinitions[featureId],
    enabled: true,
  })),
  featureConfigs: {
    // Example: Customer-specific config for ad-winners
    [PortalFeature.AD_WINNERS]: {
      defaultMetrics: ['ctr', 'cpm', 'roas'],
      maxResults: 50,
      autoRefresh: true,
    },
    // Example: Customer-specific config for fb-accounts-manager
    [PortalFeature.FB_ACCOUNTS_MANAGER]: {
      maxApps: 5,
      autoTokenRefresh: true,
      showExpiredWarnings: true,
    },
  },
};

import type { PortalConfig } from './core/types';
import { PortalFeature } from './core/types';
import { customerConfig } from '../../customers';

const featureDefinitions = {
  [PortalFeature.AD_WINNERS]: {
    id: PortalFeature.AD_WINNERS,
    name: 'Ad Winners',
    icon: 'pi pi-trophy',
    route: '/ad-winners',
  },
  [PortalFeature.CREATIVES_UPLOADER]: {
    id: PortalFeature.CREATIVES_UPLOADER,
    name: 'Creatives Uploader',
    icon: 'pi pi-cloud-upload',
    route: '/creatives-uploader',
  },
  [PortalFeature.FB_ACCOUNTS_MANAGER]: {
    id: PortalFeature.FB_ACCOUNTS_MANAGER,
    name: 'Facebook Accounts',
    icon: 'fab fa-facebook-f',
    route: '/fb-accounts-manager',
  },
};

// Default feature configurations
export const defaultFeatureConfigs: Partial<Record<PortalFeature, any>> = {
  [PortalFeature.AD_WINNERS]: {
    defaultMetrics: ['ctr', 'cpm', 'roas'],
    maxResults: 50,
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
    showCalculationFormulas: true,
  },
  [PortalFeature.CREATIVES_UPLOADER]: {
    maxFileSize: 50, // MB
    allowedFormats: ['jpg', 'png', 'mp4', 'gif'],
    autoOptimize: true,
    compressionQuality: 85,
  },
  [PortalFeature.FB_ACCOUNTS_MANAGER]: {
    maxApps: 10,
    autoTokenRefresh: true,
    showExpiredWarnings: true,
    tokenExpiryWarningDays: 7,
    maxUsersPerApp: 5,
  },
};

// Merge default configs with customer overrides
const mergedFeatureConfigs: Partial<Record<PortalFeature, any>> = {};
Object.values(PortalFeature).forEach((feature) => {
  mergedFeatureConfigs[feature] = {
    ...defaultFeatureConfigs[feature],
    ...(customerFeatureConfigs[feature] || {}),
  };
});

// Build portal config from customer config
export const portalConfig: PortalConfig = {
  supabase: customerConfig.supabase,
  features: customerConfig.enabledFeatures.map((featureId: PortalFeature) => ({
    ...featureDefinitions[featureId],
    enabled: true,
  })),
  featureConfigs: mergedFeatureConfigs,
};

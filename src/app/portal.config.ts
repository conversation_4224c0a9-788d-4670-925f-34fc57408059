import type { PortalConfig } from './types';
import {
  customerConfig,
  PortalFeature as CustomerPortalFeature,
} from '../../customers';

const featureDefinitions = {
  [CustomerPortalFeature.AD_WINNERS]: {
    id: 'ad-winners',
    name: 'Ad Winners',
    icon: 'pi pi-trophy',
    route: '/ad-winners',
  },
  [CustomerPortalFeature.CREATIVES_UPLOADER]: {
    id: 'creatives-uploader',
    name: 'Creatives Uploader',
    icon: 'pi pi-cloud-upload',
    route: '/creatives-uploader',
  },
  [CustomerPortalFeature.FB_ACCOUNTS_MANAGER]: {
    id: 'fb-accounts-manager',
    name: 'Facebook Accounts',
    icon: 'fab fa-facebook-f',
    route: '/fb-accounts-manager',
  },
};

// Build portal config from customer config
export const portalConfig: PortalConfig = {
  supabase: customerConfig.supabase,
  features: customerConfig.enabledFeatures.map(
    (featureId: CustomerPortalFeature) => ({
      ...featureDefinitions[featureId],
      enabled: true,
    }),
  ),
};

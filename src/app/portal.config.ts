import type { PortalConfig, FeatureConfigRegistry } from './core/types';
import { PortalFeature } from './core/types';
import { customerConfig } from '../../customers';
import { defaultFeatureConfigs } from './core/config/default-feature-configs';

const featureDefinitions = {
  [PortalFeature.AD_WINNERS]: {
    id: PortalFeature.AD_WINNERS,
    name: 'Ad Winners',
    icon: 'pi pi-trophy',
    route: '/ad-winners',
  },
  [PortalFeature.CREATIVES_UPLOADER]: {
    id: PortalFeature.CREATIVES_UPLOADER,
    name: 'Creatives Uploader',
    icon: 'pi pi-cloud-upload',
    route: '/creatives-uploader',
  },
  [PortalFeature.FB_ACCOUNTS_MANAGER]: {
    id: PortalFeature.FB_ACCOUNTS_MANAGER,
    name: 'Facebook Accounts',
    icon: 'fab fa-facebook-f',
    route: '/fb-accounts-manager',
  },
};

export const defaultFeatureConfigs: Partial<Record<PortalFeature, any>> = {};

// Merge default configs with customer overrides
const mergedFeatureConfigs: Partial<Record<PortalFeature, any>> = {};
Object.values(PortalFeature).forEach((feature) => {
  mergedFeatureConfigs[feature] = {
    ...defaultFeatureConfigs[feature],
    ...(customerConfig.featureConfigs?.[feature] || {}),
  };
});

// Build portal config from customer config
export const portalConfig: PortalConfig = {
  supabase: customerConfig.supabase,
  features: customerConfig.enabledFeatures.map((featureId: PortalFeature) => ({
    ...featureDefinitions[featureId],
    enabled: true,
  })),
  featureConfigs: mergedFeatureConfigs,
};

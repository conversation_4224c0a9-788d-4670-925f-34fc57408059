-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create portal_feature_config table
CREATE TABLE portal_feature_config (
    portal_feature_id TEXT PRIMARY KEY,
    config JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_portal_feature_config_created_at ON portal_feature_config(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE portal_feature_config ENABLE ROW LEVEL SECURITY;

-- Create policies - allow all operations for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON portal_feature_config
    FOR ALL USING (auth.role() = 'authenticated');

-- Create function to automatically set updated_at
CREATE OR REPLACE FUNCTION set_portal_feature_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Always update updated_at
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for portal_feature_config
CREATE TRIGGER portal_feature_config_set_updated_at
    BEFORE INSERT OR UPDATE ON portal_feature_config
    FOR EACH ROW
    EXECUTE FUNCTION set_portal_feature_config_updated_at();

-- Add comments for documentation
COMMENT ON TABLE portal_feature_config IS 'Configuration settings for portal features';
COMMENT ON COLUMN portal_feature_config.portal_feature_id IS 'Identifier of the portal feature (e.g., ad-winners, fb-accounts-manager)';
COMMENT ON COLUMN portal_feature_config.config IS 'JSON configuration object for the feature';

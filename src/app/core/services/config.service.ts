import { Injectable } from '@angular/core';
import type { PortalConfig, PortalFeature, SupabaseConfig } from '../../types';
import { portalConfig } from '../../portal.config';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private readonly config: PortalConfig = portalConfig;

  constructor() {}

  get supabase(): SupabaseConfig {
    return this.config.supabase;
  }

  get navigation(): PortalFeature[] {
    return this.config.features.filter((feature) => feature.enabled !== false);
  }
}

import { Injectable } from '@angular/core';
import { Observable, of, combineLatest } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import type { PortalConfig, PortalFeature, SupabaseConfig, PortalFeatureConfig } from '../types';
import { portalConfig } from '../../portal.config';
import { PortalFeatureConfigService } from './portal-feature-config.service';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private readonly config: PortalConfig = portalConfig;

  constructor() {}

  get supabase(): SupabaseConfig {
    return this.config.supabase;
  }

  get navigation(): PortalFeature[] {
    return this.config.features.filter((feature) => feature.enabled !== false);
  }
}

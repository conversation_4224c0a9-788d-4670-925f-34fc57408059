import { Injectable } from '@angular/core';
import type {
  CustomerPortalFeatureConfig,
  PortalConfig,
  PortalFeature,
  PortalFeatureDefinition,
  SupabaseConfig,
} from '../types';
import { portalConfig } from '../../portal.config';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private readonly config: PortalConfig = portalConfig;

  constructor() {}

  get supabase(): SupabaseConfig {
    return this.config.supabase;
  }

  get navigation(): PortalFeature[] {
    return this.config.features.filter((feature) => feature.enabled !== false);
  }

  /**
   * Get customer-specific config for a feature from portal config
   */
  getCustomerFeatureConfig(
    featureId: string,
  ): CustomerPortalFeatureConfig | null {
    const featureConfigs = this.config.featureConfigs || [];
    return (
      featureConfigs.find((fc) => fc.portal_feature_id === featureId) || null
    );
  }
}

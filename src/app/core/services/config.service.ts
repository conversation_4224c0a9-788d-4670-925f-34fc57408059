import { Injectable } from '@angular/core';
import type {
  PortalConfig,
  PortalFeatureDefinition,
  SupabaseConfig,
} from '../types';
import { PortalFeature } from '../types';
import { portalConfig } from '../../portal.config';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private readonly config: PortalConfig = portalConfig;

  constructor() {}

  get supabase(): SupabaseConfig {
    return this.config.supabase;
  }

  get navigation(): PortalFeatureDefinition[] {
    return this.config.features.filter((feature) => feature.enabled !== false);
  }

  /**
   * Get customer-specific config for a feature from portal config
   */
  getCustomerFeatureConfig(featureId: PortalFeature): any | null {
    const featureConfigs = this.config.featureConfigs || {};
    return featureConfigs[featureId] || null;
  }

  /**
   * Get effective config (global DB + customer portal config override)
   */
  getEffectiveFeatureConfig(featureId: PortalFeature): Observable<any> {
    return combineLatest([
      this.portalFeatureConfigService.getFeatureConfig(featureId),
      of(this.getCustomerFeatureConfig(featureId)),
    ]).pipe(
      map(([globalConfig, customerConfig]) => {
        const global = globalConfig?.config || {};
        const customer = customerConfig || {};

        // Customer config overrides global config
        return { ...global, ...customer };
      }),
      catchError((error) => {
        console.error('Error getting effective feature config:', error);
        return of({});
      }),
    );
  }

  /**
   * Get effective config value for a specific key
   */
  getEffectiveConfigValue<T = any>(
    featureId: PortalFeature,
    key: string,
    defaultValue?: T,
  ): Observable<T> {
    return this.getEffectiveFeatureConfig(featureId).pipe(
      map((config) => {
        return config[key] !== undefined ? config[key] : (defaultValue as T);
      }),
    );
  }

  /**
   * Check if a feature is enabled for the current customer
   */
  isFeatureEnabled(featureId: PortalFeature): Observable<boolean> {
    return this.getEffectiveConfigValue(featureId, 'enabled', true);
  }

  /**
   * Get feature configuration with fallback to defaults
   */
  getFeatureConfigWithDefaults<T = any>(
    featureId: PortalFeature,
    defaults: T,
  ): Observable<T> {
    return this.getEffectiveFeatureConfig(featureId).pipe(
      map((config) => ({ ...defaults, ...config })),
    );
  }
}

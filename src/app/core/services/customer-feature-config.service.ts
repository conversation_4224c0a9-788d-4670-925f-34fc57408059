import { Injectable } from '@angular/core';
import { Observable, of, combineLatest } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { PortalFeatureConfigService } from './portal-feature-config.service';
import { CustomerPortalFeatureConfig } from '../../../customers/types';

@Injectable({
  providedIn: 'root',
})
export class CustomerFeatureConfigService {
  private currentCustomerId: string = 'default'; // TODO: Get from auth/routing

  constructor(
    private portalFeatureConfigService: PortalFeatureConfigService,
  ) {}

  /**
   * Get customer-specific config for a feature
   */
  getCustomerFeatureConfig(featureId: string): Observable<CustomerPortalFeatureConfig | null> {
    // TODO: Load from customer config file
    // For now, return empty config
    return of(null);
  }

  /**
   * Get effective config (global + customer override)
   */
  getEffectiveFeatureConfig(featureId: string): Observable<any> {
    return combineLatest([
      this.portalFeatureConfigService.getFeatureConfig(featureId),
      this.getCustomerFeatureConfig(featureId),
    ]).pipe(
      map(([globalConfig, customerConfig]) => {
        const global = globalConfig?.config || {};
        const customer = customerConfig || {};
        
        // Customer config overrides global config
        return { ...global, ...customer };
      }),
      catchError((error) => {
        console.error('Error getting effective feature config:', error);
        return of({});
      }),
    );
  }

  /**
   * Get effective config value for a specific key
   */
  getEffectiveConfigValue<T = any>(
    featureId: string,
    key: string,
    defaultValue?: T,
  ): Observable<T> {
    return this.getEffectiveFeatureConfig(featureId).pipe(
      map((config) => {
        return config[key] !== undefined ? config[key] : (defaultValue as T);
      }),
    );
  }

  /**
   * Check if a feature is enabled for the current customer
   */
  isFeatureEnabled(featureId: string): Observable<boolean> {
    return this.getEffectiveConfigValue(featureId, 'enabled', true);
  }

  /**
   * Get feature configuration with fallback to defaults
   */
  getFeatureConfigWithDefaults<T = any>(
    featureId: string,
    defaults: T,
  ): Observable<T> {
    return this.getEffectiveFeatureConfig(featureId).pipe(
      map((config) => ({ ...defaults, ...config })),
    );
  }

  /**
   * Set current customer ID (called when customer context changes)
   */
  setCurrentCustomer(customerId: string): void {
    this.currentCustomerId = customerId;
  }

  /**
   * Get current customer ID
   */
  getCurrentCustomer(): string {
    return this.currentCustomerId;
  }
}

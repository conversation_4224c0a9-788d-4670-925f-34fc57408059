import { Injectable } from '@angular/core';
import { Observable, from, map, catchError, throwError } from 'rxjs';
import { SupabaseService } from './supabase.service';
import { PortalFeatureConfig } from '../types/config.types';

@Injectable({
  providedIn: 'root',
})
export class PortalFeatureConfigService {
  constructor(private supabaseService: SupabaseService) {}

  /**
   * Get configuration for a specific portal feature
   */
  getFeatureConfig(featureId: string): Observable<PortalFeatureConfig | null> {
    return from(
      this.supabaseService.client
        .from('portal_feature_config')
        .select('*')
        .eq('portal_feature_id', featureId)
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          if (response.error.code === 'PGRST116') {
            // No rows found
            return null;
          }
          console.error('Error fetching feature config:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error fetching feature config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Get all portal feature configurations
   */
  getAllFeatureConfigs(): Observable<PortalFeatureConfig[]> {
    return from(
      this.supabaseService.client
        .from('portal_feature_config')
        .select('*')
        .order('portal_feature_id', { ascending: true }),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching all feature configs:', response.error);
          throw response.error;
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching all feature configs:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Create or update configuration for a portal feature
   */
  upsertFeatureConfig(
    featureId: string,
    config: any,
  ): Observable<PortalFeatureConfig> {
    const configData = {
      portal_feature_id: featureId,
      config: config,
    };

    return from(
      this.supabaseService.client
        .from('portal_feature_config')
        .upsert([configData], {
          onConflict: 'portal_feature_id',
          ignoreDuplicates: false,
        })
        .select()
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error upserting feature config:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error upserting feature config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Update configuration for a portal feature
   */
  updateFeatureConfig(
    featureId: string,
    config: any,
  ): Observable<PortalFeatureConfig> {
    return from(
      this.supabaseService.client
        .from('portal_feature_config')
        .update({ config: config })
        .eq('portal_feature_id', featureId)
        .select()
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error updating feature config:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error updating feature config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Delete configuration for a portal feature
   */
  deleteFeatureConfig(featureId: string): Observable<void> {
    return from(
      this.supabaseService.client
        .from('portal_feature_config')
        .delete()
        .eq('portal_feature_id', featureId),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error deleting feature config:', response.error);
          throw response.error;
        }
      }),
      catchError((error) => {
        console.error('Error deleting feature config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Get configuration value for a specific key within a feature
   */
  getConfigValue<T = any>(
    featureId: string,
    key: string,
    defaultValue?: T,
  ): Observable<T> {
    return this.getFeatureConfig(featureId).pipe(
      map((config) => {
        if (!config || !config.config) {
          return defaultValue as T;
        }
        return config.config[key] !== undefined
          ? config.config[key]
          : (defaultValue as T);
      }),
    );
  }

  /**
   * Set configuration value for a specific key within a feature
   */
  setConfigValue(
    featureId: string,
    key: string,
    value: any,
  ): Observable<PortalFeatureConfig> {
    return this.getFeatureConfig(featureId).pipe(
      map((existingConfig) => {
        const currentConfig = existingConfig?.config || {};
        return {
          ...currentConfig,
          [key]: value,
        };
      }),
      switchMap((newConfig) =>
        this.upsertFeatureConfig(featureId, newConfig),
      ),
    );
  }
}

// Import switchMap
import { switchMap } from 'rxjs/operators';

import { PortalFeature } from '../types/config.types';
import { FeatureConfigRegistry } from '../types/feature-configs';
import { defaultAdWinnersConfig } from '../../pages/ad-winners/config/ad-winners.config';
import { defaultFbAccountsManagerConfig } from '../../pages/fb-accounts-manager/config/fb-accounts-manager.config';
import { defaultCreativesUploaderConfig } from '../../pages/creatives-uploader/config/creatives-uploader.config';

// Type-safe default configurations for all features
export const defaultFeatureConfigs: FeatureConfigRegistry = {
  [PortalFeature.AD_WINNERS]: defaultAdWinnersConfig,
  [PortalFeature.FB_ACCOUNTS_MANAGER]: defaultFbAccountsManagerConfig,
  [PortalFeature.CREATIVES_UPLOADER]: defaultCreativesUploaderConfig,
};

// Helper function to get default config for a specific feature
export function getDefaultFeatureConfig<T extends PortalFeature>(
  featureId: T,
): FeatureConfigRegistry[T] {
  return defaultFeatureConfigs[featureId];
}

export enum PortalFeature {
  AD_WINNERS = 'ad-winners',
  CREATIVES_UPLOADER = 'creatives-uploader',
  FB_ACCOUNTS_MANAGER = 'fb-accounts-manager',
}

export interface SupabaseConfig {
  url: string;
  anonKey: string;
}

export interface PortalFeature {
  id: string;
  name: string;
  icon: string;
  route: string;
  enabled?: boolean;
  roles?: string[];
}

export interface PortalFeatureConfig {
  portal_feature_id: string;
  config: any;
  created_at?: string;
  updated_at?: string;
}

export interface CustomerPortalFeatureConfig {
  portal_feature_id: string;
  config: any;
}

export interface PortalConfig {
  supabase: SupabaseConfig;
  features: PortalFeature[];
  featureConfigs?: CustomerPortalFeatureConfig[];
}

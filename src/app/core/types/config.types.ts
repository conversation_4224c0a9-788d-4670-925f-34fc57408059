export enum PortalFeature {
  AD_WINNERS = 'ad-winners',
  CREATIVES_UPLOADER = 'creatives-uploader',
  FB_ACCOUNTS_MANAGER = 'fb-accounts-manager',
}

export interface SupabaseConfig {
  url: string;
  anonKey: string;
}

export interface PortalFeatureDefinition {
  id: PortalFeature;
  name: string;
  icon: string;
  route: string;
  enabled?: boolean;
  roles?: string[];
}

export interface PortalFeatureConfig {
  portal_feature_id: PortalFeature;
  config: any;
  created_at?: string;
  updated_at?: string;
}

export interface PortalConfig {
  supabase: SupabaseConfig;
  features: PortalFeatureDefinition[];
  featureConfigs?: Partial<{
    [K in PortalFeature]: any; // Will be overridden by FeatureConfigRegistry
  }>;
}

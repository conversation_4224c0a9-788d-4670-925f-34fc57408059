export interface FacebookAdAccount {
  id: string;
  name: string;
  account_status: number;
  currency: string;
  timezone_name?: string;
  business_id?: string;
  business_name?: string;
}

export interface FacebookUserWithAccounts {
  user: {
    id: string;
    app_id: string;
    facebook_user_id: string;
    name: string;
    email?: string;
    profile_picture_url?: string;
    is_long_lived: boolean;
    permissions?: string[];
    expires_at?: string;
    last_login?: string;
    created_at: string;
  };
  adAccounts: FacebookAdAccount[];
}

export interface FacebookAppWithUsers {
  app: {
    id: string;
    app_id: string;
    name: string;
    description?: string;
    is_active: boolean;
    created_at: string;
  };
  users: FacebookUserWithAccounts[];
}

export interface FacebookAccountsStructure {
  apps: FacebookAppWithUsers[];
}

// Helper types for feature configuration
export interface SelectedFacebookAccount {
  appId: string;
  appName: string;
  userId: string;
  userName: string;
  adAccountIds: string[];
}

export interface FacebookAccountSelection {
  selectedAccounts: SelectedFacebookAccount[];
  selectionMode: 'manual' | 'all';
  autoIncludeNewAccounts: boolean;
  lastUpdated: string;
}

// Account status helpers
export enum FacebookAccountStatus {
  ACTIVE = 1,
  DISABLED = 2,
  UNSETTLED = 3,
  PENDING_RISK_REVIEW = 7,
  PENDING_SETTLEMENT = 8,
  IN_GRACE_PERIOD = 9,
  TEMPORARILY_UNAVAILABLE = 101,
  PENDING_CLOSURE = 102,
}

export function getAccountStatusLabel(status: number): string {
  switch (status) {
    case FacebookAccountStatus.ACTIVE:
      return 'Active';
    case FacebookAccountStatus.DISABLED:
      return 'Disabled';
    case FacebookAccountStatus.UNSETTLED:
      return 'Unsettled';
    case FacebookAccountStatus.PENDING_RISK_REVIEW:
      return 'Pending Risk Review';
    case FacebookAccountStatus.PENDING_SETTLEMENT:
      return 'Pending Settlement';
    case FacebookAccountStatus.IN_GRACE_PERIOD:
      return 'In Grace Period';
    case FacebookAccountStatus.TEMPORARILY_UNAVAILABLE:
      return 'Temporarily Unavailable';
    case FacebookAccountStatus.PENDING_CLOSURE:
      return 'Pending Closure';
    default:
      return 'Unknown';
  }
}

export function getAccountStatusSeverity(status: number): 'success' | 'warning' | 'danger' | 'info' {
  switch (status) {
    case FacebookAccountStatus.ACTIVE:
      return 'success';
    case FacebookAccountStatus.DISABLED:
    case FacebookAccountStatus.PENDING_CLOSURE:
      return 'danger';
    case FacebookAccountStatus.UNSETTLED:
    case FacebookAccountStatus.PENDING_RISK_REVIEW:
    case FacebookAccountStatus.PENDING_SETTLEMENT:
    case FacebookAccountStatus.IN_GRACE_PERIOD:
      return 'warning';
    default:
      return 'info';
  }
}

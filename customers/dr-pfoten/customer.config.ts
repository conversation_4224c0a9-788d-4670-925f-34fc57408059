import type { CustomerConfig } from '../types';
import { PortalFeature } from '../../src/app/core/types';

export const customerConfig: CustomerConfig = {
  name: 'Dr. <PERSON><PERSON><PERSON>',
  supabase: {
    url: 'https://jthdagdrrxcyfsulrnql.supabase.co',
    anon<PERSON>ey:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0aGRhZ2RycnhjeWZzdWxybnFsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMjIzNDUsImV4cCI6MjA2NDc5ODM0NX0.FUogH3xIT8fnuAiVLnT1y6CsD_RY__EknRE4XRFVhHs',
  },
  enabledFeatures: [PortalFeature.AD_WINNERS, PortalFeature.CREATIVES_UPLOADER],
};

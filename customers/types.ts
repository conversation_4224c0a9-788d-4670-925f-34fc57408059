export enum PortalFeature {
  AD_WINNERS = 'ad-winners',
  CREATIVES_UPLOADER = 'creatives-uploader',
  FB_ACCOUNTS_MANAGER = 'fb-accounts-manager',
}

export interface CustomerSupabaseConfig {
  url: string;
  anonKey: string;
}

export interface CustomerPortalFeatureConfig {
  [key: string]: any;
}

export interface CustomerConfig {
  name: string;
  supabase: CustomerSupabaseConfig;
  enabledFeatures: PortalFeature[];
  featureConfigs?: Partial<Record<PortalFeature, CustomerPortalFeatureConfig>>;
}

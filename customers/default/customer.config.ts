import type { CustomerConfig } from '../types';
import { PortalFeature } from '../types';

export const customerConfig: CustomerConfig = {
  name: 'Default Customer',
  supabase: {
    url: 'https://geikrwrlglsnkspgcrzg.supabase.co',
    anon<PERSON>ey:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.WCuwesgjBbRS8ps4nu7FpolpqU1om-1IuvGFp6Wqspg',
  },
  enabledFeatures: [
    PortalFeature.AD_WINNERS,
    PortalFeature.CREATIVES_UPLOADER,
    PortalFeature.FB_ACCOUNTS_MANAGER,
  ],
  featureConfigs: [
    {
      portal_feature_id: 'ad-winners',
      config: {
        defaultMetrics: ['ctr', 'cpm', 'roas'],
        maxResults: 50,
        autoRefresh: true,
      },
    },
  ],
};

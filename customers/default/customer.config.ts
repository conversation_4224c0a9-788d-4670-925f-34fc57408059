import type { CustomerConfig } from '../types';
import { PortalFeature } from '../types';

export const customerConfig: CustomerConfig = {
  name: 'Default Customer',
  supabase: {
    url: 'https://geikrwrlglsnkspgcrzg.supabase.co',
    anonKey:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.WCuwesgjBbRS8ps4nu7FpolpqU1om-1IuvGFp6Wqspg',
  },
  enabledFeatures: [
    PortalFeature.AD_WINNERS,
    PortalFeature.CREATIVES_UPLOADER,
    PortalFeature.FB_ACCOUNTS_MANAGER,
  ],
};

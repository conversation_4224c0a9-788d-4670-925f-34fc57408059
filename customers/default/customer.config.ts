import type { CustomerConfig } from '../types';
import { PortalFeature } from '../types';

export const customerConfig: CustomerConfig = {
  name: 'Default Customer',
  supabase: {
    url: 'https://jthdagdrrxcyfsulrnql.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.FUogH3xIT8fnuAiVLnT1y6CsD_RY__EknRE4XRFVhHs',
  },
  enabledFeatures: [
    PortalFeature.AD_WINNERS,
    PortalFeature.CREATIVES_UPLOADER,
    PortalFeature.FB_ACCOUNTS_MANAGER,
  ],
};
